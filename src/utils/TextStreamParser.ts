export interface StreamOutput {
  message_id_type: string
  content: string
}

type ParserState = 'TEXT' | 'IN_XML' | 'IN_VOICE'
type ContentType = 'text' | 'xml' | 'voice'

const BR_TAG = '<br>'
const VOICE_START_TAG = '```voice\n'
const VOICE_END_REGEX = /\n\s*```\s*/

// 定义系统中实际使用的XML标签类型
const KNOWN_XML_TAGS = [
  'tina_task', // 任务相关标签
  'card', // 卡片标签
  'user_maybe_say', // 用户建议标签
  'tina_analyze', // 分析标签（用于测试和分析场景）
  'tina_memory', // 分析标签（用于测试和分析场景）
  'analyze',
  // 错误的标签不进入 text
  'ina_analyze',
  'ina_memory',
  'memory',
] as const

// 创建匹配已知XML标签的正则表达式
const XML_START_REGEX = new RegExp(
  `^<(${KNOWN_XML_TAGS.join('|')})(?:\\s+[^>]*)?>`,
)

export class TextStreamParser {
  private baseId: string
  private segmentIndex: number = 0
  private buffer: string = ''
  private currentState: ParserState = 'TEXT'

  // 状态相关数据
  private xmlTagName: string | null = null
  private xmlStartTag: string = '' // 只保存起始标签

  // 内容累加器
  private currentTextContent: string = ''
  private currentVoiceContent: string = ''

  // 表格检测状态
  private isInMarkdownTable: boolean = false

  // 代码块检测状态
  private isInCodeBlock: boolean = false

  // 全局内容缓冲区，用于跨段落的模式检测
  private globalContentBuffer: string = ''

  constructor(baseId: string) {
    this.baseId = baseId
  }

  private getFullId(type: ContentType): string {
    return `${this.baseId}-${this.segmentIndex}:${type}`
  }

  // 检测是否在markdown表格中
  private updateMarkdownTableState(content: string) {
    // 检查是否包含表格行（以|开头或结尾，或包含|分隔符）
    const lines = content.split('\n')
    let hasTableIndicators = false

    for (const line of lines) {
      const trimmedLine = line.trim()
      // 检测表格行：包含管道符且不是代码块
      if (
        trimmedLine.includes('|') &&
        !trimmedLine.startsWith('```') &&
        !trimmedLine.endsWith('```')
      ) {
        // 进一步验证是否为表格行（至少包含2个管道符或以管道符开头/结尾）
        const pipeCount = (trimmedLine.match(/\|/g) || []).length
        if (
          pipeCount >= 2 ||
          trimmedLine.startsWith('|') ||
          trimmedLine.endsWith('|')
        ) {
          hasTableIndicators = true
          break
        }
      }
    }

    this.isInMarkdownTable = hasTableIndicators
  }

  // 检测BR标签是否在表格上下文中
  private isBrInTableContext(): boolean {
    // 检查BR标签前后的内容来判断是否在表格中
    const beforeBr = this.currentTextContent
    const afterBr = this.buffer.substring(4) // 跳过 <br> 标签

    // 获取BR前后各几行内容进行分析
    const beforeLines = beforeBr.split('\n').slice(-3) // 前3行
    const afterLines = afterBr.split('\n').slice(0, 3) // 后3行
    const contextLines = [...beforeLines, ...afterLines]

    // 检查上下文中是否有表格行
    for (const line of contextLines) {
      const trimmedLine = line.trim()
      if (
        trimmedLine.includes('|') &&
        !trimmedLine.startsWith('```') &&
        !trimmedLine.endsWith('```')
      ) {
        const pipeCount = (trimmedLine.match(/\|/g) || []).length
        if (
          pipeCount >= 2 ||
          trimmedLine.startsWith('|') ||
          trimmedLine.endsWith('|')
        ) {
          return true
        }
      }
    }

    return false
  }

  // 检测和更新代码块状态
  private updateCodeBlockState(content: string) {
    // 将新内容添加到全局缓冲区
    this.globalContentBuffer += content

    // 先移除所有完整的 voice 块，避免干扰代码块计数
    let cleanedContent = this.globalContentBuffer

    // 移除完整的 voice 块（包括开始和结束标记）
    cleanedContent = cleanedContent.replace(/```voice[\s\S]*?\n\s*```/g, '')

    // 移除未完成的 voice 块开始标记（流式解析时可能出现）
    cleanedContent = cleanedContent.replace(/```voice[\s\S]*$/g, '')

    // 现在检测剩余内容中的代码块标记
    // 匹配 ```xxx 或单独的 ```
    const codeBlockPattern = /```[a-zA-Z]*|```(?=\s|$)/g

    let codeBlockCount = 0

    // 重置匹配位置
    codeBlockPattern.lastIndex = 0

    // 计算清理后内容中的代码块标记数量
    while (codeBlockPattern.exec(cleanedContent) !== null) {
      codeBlockCount++
    }

    // 奇数个标记表示在代码块内，偶数个表示在代码块外
    this.isInCodeBlock = codeBlockCount % 2 === 1
  }

  // 开始一个新段落
  private startNewSegment() {
    this.segmentIndex++
    this.currentTextContent = ''
    this.currentVoiceContent = ''
    this.isInMarkdownTable = false // 重置表格状态
    // 注意：不重置代码块状态，因为它是全局状态
  }

  /**
   * 流式/非流式调用同接口
   * @param text 新增文本
   * @param isFinal 是否是最后一块
   */
  processText(text: string, isFinal = false): StreamOutput[] {
    const results: StreamOutput[] = []
    this.buffer += text

    let progressMade = true
    while (progressMade) {
      progressMade = false

      switch (this.currentState) {
        case 'TEXT': {
          const firstAngle = this.buffer.indexOf('<')
          // 只检查 ```voice\n 格式的反引号，其他反引号当作普通文本处理
          const voiceStartIndex = this.buffer.indexOf(VOICE_START_TAG)

          // 只将角括号和voice格式的反引号作为潜在的分割点
          const potentialStarts = [firstAngle, voiceStartIndex].filter(
            (i) => i !== -1,
          )

          let safeText = ''
          const splitPoint =
            potentialStarts.length > 0
              ? Math.min(...potentialStarts)
              : this.buffer.length

          if (splitPoint > 0) {
            safeText = this.buffer.substring(0, splitPoint)
            this.buffer = this.buffer.substring(splitPoint)
            this.currentTextContent += safeText
            this.updateMarkdownTableState(this.currentTextContent)
            this.updateCodeBlockState(safeText)
            results.push({
              message_id_type: this.getFullId('text'),
              content: this.currentTextContent,
            })
            progressMade = true
          }

          if (this.buffer.startsWith(BR_TAG)) {
            // 使用上下文检测来判断BR标签是否在表格中
            if (this.isBrInTableContext()) {
              // 在表格中，BR 标签作为普通文本处理，不分割段落
              this.currentTextContent += BR_TAG
              this.buffer = this.buffer.substring(BR_TAG.length)
              // 不立即输出，继续处理后续内容
              progressMade = true
            } else {
              // 不在表格中，按原逻辑处理（开始新段落）
              this.buffer = this.buffer.substring(BR_TAG.length)
              this.startNewSegment()
              progressMade = true
            }
            break
          }

          // 处理 voice 块开始
          if (this.buffer.startsWith(VOICE_START_TAG)) {
            // 将 voice 开始标记添加到全局缓冲区
            this.globalContentBuffer += VOICE_START_TAG
            this.startNewSegment()
            this.currentState = 'IN_VOICE'
            this.buffer = this.buffer.substring(VOICE_START_TAG.length)
            progressMade = true
            break
          }

          // 在代码块内部时，跳过 XML 匹配，将所有内容作为文本处理
          if (this.isInCodeBlock) {
            // 在代码块内部，处理所有内容作为普通文本
            // 寻找安全的分割点（换行符或缓冲区末尾）
            let splitPoint = this.buffer.indexOf('\n')
            if (splitPoint === -1) {
              splitPoint = this.buffer.length
            } else {
              splitPoint += 1 // 包含换行符
            }

            if (splitPoint > 0) {
              const textContent = this.buffer.substring(0, splitPoint)
              this.currentTextContent += textContent
              this.buffer = this.buffer.substring(splitPoint)
              this.updateCodeBlockState(textContent)
              results.push({
                message_id_type: this.getFullId('text'),
                content: this.currentTextContent,
              })
              progressMade = true
            }
            break
          } else {
            // 不在代码块内部，正常处理 XML 标签

            // 处理特殊 xml 标签
            const xmlMatch = this.isKnownXmlStartTag(this.buffer)
            if (xmlMatch) {
              this.startNewSegment()
              this.currentState = 'IN_XML'
              this.xmlTagName = xmlMatch[1]
              this.xmlStartTag = xmlMatch[0]
              this.buffer = this.buffer.substring(this.xmlStartTag.length)
              progressMade = true
              break
            }

            // 处理非特殊 xml 标签，归为普通文本
            if (this.isOtherXmlTag(this.buffer)) {
              const idx = this.buffer.indexOf('>')
              if (idx !== -1) {
                const xmlText = this.buffer.substring(0, idx + 1)
                this.currentTextContent += xmlText
                this.buffer = this.buffer.substring(idx + 1)
                results.push({
                  message_id_type: this.getFullId('text'),
                  content: this.currentTextContent,
                })
                progressMade = true
              } else {
                // 标签不完整，等待更多数据
              }
              break
            }
          }

          // 没匹配到任何特殊，结束循环
          break
        }

        case 'IN_XML': {
          if (!this.xmlTagName) {
            // 理论上不可能，重置状态以避免死循环
            this.currentState = 'TEXT'
            this.startNewSegment()
            break
          }

          const endTag = `</${this.xmlTagName}>`
          const endTagIndex = this.buffer.indexOf(endTag)

          if (endTagIndex !== -1) {
            // 找到了！
            const xmlContent = this.buffer.substring(0, endTagIndex)
            const fullXmlBlock = this.xmlStartTag + xmlContent + endTag

            // 原子性输出
            results.push({
              message_id_type: this.getFullId('xml'),
              content: fullXmlBlock,
            })

            this.buffer = this.buffer.substring(endTagIndex + endTag.length)
            this.currentState = 'TEXT'
            this.startNewSegment()
            progressMade = true
          } else {
            // 未找到结束标签，等待更多数据
          }
          break
        }

        case 'IN_VOICE': {
          const fullContent = this.currentVoiceContent + this.buffer
          const endMatch = fullContent.match(VOICE_END_REGEX)

          if (endMatch && endMatch.index !== undefined) {
            // 找到结尾
            const voiceContent = fullContent.substring(0, endMatch.index)
            const voiceContentWithEndTag = voiceContent + endMatch[0].trim()

            // 将 voice 内容和结束标记添加到全局缓冲区
            this.globalContentBuffer += voiceContent + endMatch[0]

            results.push({
              message_id_type: this.getFullId('voice'),
              content: voiceContentWithEndTag,
            })
            this.buffer = fullContent.substring(
              endMatch.index + endMatch[0].length,
            )
            this.currentState = 'TEXT'
            this.startNewSegment()
            progressMade = true
          } else {
            // 没找到结尾，检查是否有XML标签需要先处理
            const xmlIdx = this.buffer.indexOf('<')
            if (xmlIdx !== -1) {
              const candidate = this.buffer.substring(xmlIdx)
              const xmlMatch = this.isKnownXmlStartTag(candidate)
              if (xmlMatch) {
                // 先输出 voice 中 xml 标签之前内容
                const beforeXml = this.buffer.substring(0, xmlIdx)
                if (beforeXml) {
                  this.currentVoiceContent += beforeXml
                  results.push({
                    message_id_type: this.getFullId('voice'),
                    content: this.currentVoiceContent,
                  })
                }
                this.buffer = this.buffer.substring(xmlIdx + xmlMatch[0].length)
                this.currentState = 'IN_XML'
                this.xmlTagName = xmlMatch[1]
                this.xmlStartTag = xmlMatch[0]
                this.startNewSegment()
                progressMade = true
              } else {
                // 没匹配特殊xml，作为普通内容继续
                this.currentVoiceContent += this.buffer
                results.push({
                  message_id_type: this.getFullId('voice'),
                  content: this.currentVoiceContent,
                })
                this.buffer = ''
              }
            } else if (this.buffer.length > 0) {
              // 全部作为 voice 内容输出
              this.currentVoiceContent += this.buffer
              results.push({
                message_id_type: this.getFullId('voice'),
                content: this.currentVoiceContent,
              })
              this.buffer = ''
            }
          }
          break
        }
      }
    }

    // 末尾时强制flush剩余内容
    if (isFinal && this.buffer.length > 0) {
      switch (this.currentState) {
        case 'TEXT':
          this.currentTextContent += this.buffer
          results.push({
            message_id_type: this.getFullId('text'),
            content: this.currentTextContent,
          })
          this.buffer = ''
          break
        case 'IN_VOICE':
          this.currentVoiceContent += this.buffer
          results.push({
            message_id_type: this.getFullId('voice'),
            content: this.currentVoiceContent,
          })
          this.buffer = ''
          break
        case 'IN_XML':
          // 不完整 xml 作为文本输出
          const incomplete = this.xmlStartTag + this.buffer
          results.push({
            message_id_type: this.getFullId('text'),
            content: incomplete,
          })
          this.buffer = ''
          break
      }
    }
    // 结束后开启新片段，让相同 id 的 message 可以产生新的段落
    if (isFinal) {
      this.startNewSegment()
    }

    return results
  }

  finalize(): StreamOutput[] {
    return this.processText('', true)
  }

  // 判断是否是已知xml标签起始
  private isKnownXmlStartTag(buffer: string): RegExpMatchArray | null {
    return buffer.match(XML_START_REGEX)
  }

  // 判断是否是其他xml标签起始（非已知标签）
  private isOtherXmlTag(buffer: string): boolean {
    if (buffer.startsWith('<')) {
      const genericMatch = buffer.match(/^<([a-zA-Z0-9\-]+)(?:\s[^>]*)?>/)
      if (genericMatch) {
        return !KNOWN_XML_TAGS.includes(genericMatch[1] as any)
      }
    }
    return false
  }
}
