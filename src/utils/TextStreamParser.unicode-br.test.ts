import { describe, it, expect } from 'vitest'
import { TextStreamParser } from '@/utils/TextStreamParser'

/**
 * Unicode BR 标签解析测试
 *
 * 测试 TextStreamParser 对 Unicode 转义形式的 <br> 标签的处理能力
 * 特别是在包含 Markdown 表格的复杂内容中的表现
 */
describe('TextStreamParser - Unicode BR 标签处理', () => {
  it('应该正确处理包含 Unicode 转义 BR 标签的内容', () => {
    // 用户提供的实际内容 - JSON 中的 \u003cbr\u003e 会被解析为 <br>
    const content = `根：

<br>

### **2025**
| 指标               | 2025年预测 | 常年平均值 | 对比情况 |
|--------------------|------------|------------|----------|
| 西北太平洋台风生成数 | 4-5个      | 3.8个      | **偏多** |
| 影响      | 2-3个      | 1.8个      | **偏多** |

<br>

### **主要原因**
1. **海温偏高**：当成
2. **季风活跃**：西南强
3. **大气环流配置**：副行

<br>

### **需注意**
- 台存在
- 7注华南、东南沿海

需？`

    console.log('=== 测试内容 ===')
    console.log('原始内容长度:', content.length)
    console.log('包含的 BR 标签数量:', (content.match(/<br>/g) || []).length)
    console.log('包含表格:', content.includes('|'))
    console.log('实际内容预览:', content.substring(0, 100))

    const parser = new TextStreamParser('unicode-br-test')
    const outputs = parser.processText(content, true)

    console.log('\n=== 解析结果 ===')
    console.log('输出段落数量:', outputs.length)

    outputs.forEach((output, index) => {
      console.log(`\n段落 ${index + 1}:`)
      console.log(`  类型: ${output.message_id_type}`)
      console.log(`  长度: ${output.content.length}`)
      console.log(
        `  内容预览: ${output.content.substring(0, 100)}${output.content.length > 100 ? '...' : ''}`,
      )
      console.log(
        `  包含 Unicode BR: ${output.content.includes('\u003cbr\u003e')}`,
      )
      console.log(`  包含表格: ${output.content.includes('|')}`)
    })

    // 验证基本功能
    expect(outputs.length).toBeGreaterThan(1) // 应该被 BR 标签分割成多个段落

    // 重构完整内容
    const reconstructedContent = outputs.map((o) => o.content).join('')
    console.log('\n=== 内容重构验证 ===')
    console.log('重构内容长度:', reconstructedContent.length)
    console.log('原始内容长度:', content.length)

    // 验证关键内容是否完整保留
    expect(reconstructedContent).toContain('根：')
    expect(reconstructedContent).toContain('### **2025**')
    expect(reconstructedContent).toContain('| 指标')
    expect(reconstructedContent).toContain('西北太平洋台风生成数')
    expect(reconstructedContent).toContain('### **主要原因**')
    expect(reconstructedContent).toContain('**海温偏高**：当成')
    expect(reconstructedContent).toContain('**季风活跃**：西南强')
    expect(reconstructedContent).toContain('**大气环流配置**：副行')
    expect(reconstructedContent).toContain('### **需注意**')
    expect(reconstructedContent).toContain('台存在')
    expect(reconstructedContent).toContain('7注华南、东南沿海')
    expect(reconstructedContent).toContain('需？')

    // 验证表格内容是否正确处理
    const tableOutputs = outputs.filter((o) => o.content.includes('|'))
    console.log('\n=== 表格处理验证 ===')
    console.log('包含表格的段落数量:', tableOutputs.length)

    if (tableOutputs.length > 0) {
      const tableContent = tableOutputs[0].content
      console.log('表格内容长度:', tableContent.length)
      console.log('表格内容包含 BR:', tableContent.includes('<br>'))

      // 在表格中，Unicode BR 标签应该被保留而不是用于分割
      expect(tableContent).toContain('| 指标')
      expect(tableContent).toContain('西北太平洋台风生成数')
      expect(tableContent).toContain('4-5个')
      expect(tableContent).toContain('**偏多**')
    }
  })

  it('应该正确处理流式输入中的 BR 标签', () => {
    const content = `前面内容<br>后面内容`

    const parser = new TextStreamParser('streaming-unicode-br')
    const allOutputs: any[] = []

    // 模拟流式输入，将内容分成小块
    const chunkSize = 1
    for (let i = 0; i < content.length; i += chunkSize) {
      const chunk = content.substring(i, i + chunkSize)
      const isFinal = i + chunkSize >= content.length
      const outputs = parser.processText(chunk, isFinal)
      allOutputs.push(...outputs)
    }

    console.log('\n=== 流式处理结果 ===')
    allOutputs.forEach((output, index) => {
      console.log(
        `段落 ${index + 1}: ${output.message_id_type} - "${output.content}"`,
      )
    })

    // 验证内容被正确分割
    expect(allOutputs.length).toBeGreaterThan(1)

    const reconstructed = allOutputs.map((o) => o.content).join('')
    expect(reconstructed).toContain('前面内容')
    expect(reconstructed).toContain('后面内容')
    // BR 标签本身不应该出现在最终输出中（除非在表格中）
    expect(reconstructed).not.toContain('<br>')
  })

  it('应该在表格中保留 BR 标签', () => {
    const tableContent = `| 列1 | 列2 |
|-----|-----|
| 内容1<br>换行 | 内容2 |`

    const parser = new TextStreamParser('table-unicode-br')
    const outputs = parser.processText(tableContent, true)

    console.log('\n=== 表格中的 Unicode BR 处理 ===')
    outputs.forEach((output, index) => {
      console.log(`段落 ${index + 1}: ${output.message_id_type}`)
      console.log(`内容: "${output.content}"`)
      console.log(`包含 BR: ${output.content.includes('<br>')}`)
    })

    // 在表格中，Unicode BR 标签应该被保留
    const tableOutputWithBr = outputs.find(
      (o) => o.content.includes('|') && o.content.includes('<br>'),
    )
    expect(tableOutputWithBr).toBeDefined()
    expect(tableOutputWithBr!.content).toContain('<br>')
    expect(tableOutputWithBr!.content).toContain('内容1<br>换行')
  })

  it('应该正确处理混合的 BR 标签', () => {
    const mixedContent = `开始<br>普通BR<br>另一个BR<br>结束`

    const parser = new TextStreamParser('mixed-br')
    const outputs = parser.processText(mixedContent, true)

    console.log('\n=== 混合 BR 标签处理 ===')
    outputs.forEach((output, index) => {
      console.log(`段落 ${index + 1}: "${output.content}"`)
    })

    // 应该被分割成多个段落
    expect(outputs.length).toBeGreaterThan(1)

    const reconstructed = outputs.map((o) => o.content).join('')
    expect(reconstructed).toContain('开始')
    expect(reconstructed).toContain('普通BR')
    expect(reconstructed).toContain('另一个BR')
    expect(reconstructed).toContain('结束')

    // BR 标签本身不应该出现在最终输出中
    expect(reconstructed).not.toContain('<br>')
  })
})
